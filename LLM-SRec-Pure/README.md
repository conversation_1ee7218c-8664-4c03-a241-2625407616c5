# LLM-SRec-Pure: 纯正大小模型协同推荐系统

## 📖 项目概述

基于LLM-SRec的核心思想，构建一个纯正的大小模型协同推荐系统。该系统专注于大小模型之间的协同机制，去除了联邦学习的复杂性，实现更简洁高效的推荐架构。

## 🏗️ 系统架构

### 核心协同架构
```mermaid
graph TB
    subgraph "客户端层"
        A[用户设备] --> B[CF-SRec模型]
        B --> C[用户表示μ]
    end

    subgraph "云端层"
        D[云端LLM] --> E[推荐优化]
        E --> F[推荐分数计算]
        F --> G[Top-K推荐列表]
    end

    subgraph "知识蒸馏层"
        H[教师知识生成] -.-> I[深度学习]
        I -.-> J[模型更新]
    end

    C --> D
    G --> K[接收推荐列表]
    K -.-> B

    E -.-> H
    J -.-> B

    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style H fill:#fff3e0
```

### 简化流程图
```
用户交互序列 → 小模型(CF-SRec) → 用户表示μ → 大模型(LLM) → 推荐结果
                    ↓                           ↓
                特征提取              知识蒸馏 + 推荐优化
                    ↑                           ↑
                知识反馈 ← ← ← ← ← ← ← ← ← ← ← ← ←
```

### 核心组件
1. **小模型层**: 基于CF-SRec的轻量级序列推荐模型
2. **大模型层**: 基于LLM的推荐性能优化模型
3. **协同机制**: 知识蒸馏 + 特征对齐 + 联合训练
4. **推荐引擎**: 端到端推荐结果生成

## 📁 项目结构

```
LLM-SRec-Pure/
├── README.md                          # 项目说明
├── requirements.txt                   # 依赖包
├── config/
│   └── collaborative_config.yaml     # 协同训练配置
├── models/
│   ├── small_model/                   # 小模型组件
│   │   ├── cf_srec.py                # CF-SRec序列推荐模型
│   │   └── sequence_encoder.py       # 序列编码器
│   ├── large_model/                   # 大模型组件
│   │   ├── llm_recommender.py        # LLM推荐器
│   │   └── llm_adapter.py            # LLM适配器
│   └── collaborative/                 # 协同模型
│       ├── collaborative_model.py     # 主协同模型
│       └── knowledge_distillation.py  # 知识蒸馏模块
├── training/
│   ├── collaborative_trainer.py      # 协同训练器
│   ├── small_model_trainer.py        # 小模型训练器
│   └── large_model_trainer.py        # 大模型训练器
├── utils/
│   ├── data_utils.py                 # 数据处理工具
│   ├── evaluation.py                # 评估工具
│   └── model_utils.py                # 模型工具
├── scripts/
│   ├── train_collaborative.py        # 协同训练脚本
│   ├── train_small_model.py          # 小模型训练脚本
│   └── inference.py                  # 推理脚本
└── demo/
    ├── demo_collaborative.py         # 协同演示
    └── demo_inference.py             # 推理演示
```

## 🔧 核心特性

### 1. 简化架构
- **去除联邦学习**: 不涉及多客户端、模型聚合等复杂机制
- **专注协同**: 集中精力优化大小模型协同效果
- **统一训练**: 在同一环境下进行端到端训练

### 2. 协同机制
- **知识蒸馏**: 大模型向小模型传递知识
- **特征对齐**: 大小模型特征空间对齐
- **联合优化**: 大小模型联合训练优化

### 3. 技术特点
- **计算效率**: 小模型负责快速推理，大模型负责性能优化
- **推荐效果**: 结合序列建模和语言理解能力
- **可扩展性**: 支持不同规模的大小模型组合

## 🚀 快速开始

### 环境配置
```bash
# 创建虚拟环境
conda create -n llm-srec-pure python=3.8
conda activate llm-srec-pure

# 安装依赖
pip install -r requirements.txt
```

### 数据准备
```bash
# 准备数据集
python scripts/prepare_data.py --dataset Movies_and_TV

# 数据预处理
python utils/data_utils.py --preprocess
```

### 模型训练
```bash
# 1. 小模型预训练
python scripts/train_small_model.py --config config/collaborative_config.yaml

# 2. 大小模型协同训练
python scripts/train_collaborative.py --config config/collaborative_config.yaml

# 3. 模型推理
python scripts/inference.py --model_path ./checkpoints/best_model
```

## 📊 实验配置

### 基础配置
```yaml
# 小模型配置
small_model:
  model_type: "cf_srec"
  hidden_units: 64
  num_blocks: 2
  num_heads: 1
  max_sequence_length: 128

# 大模型配置  
large_model:
  model_type: "llm_recommender"
  llm_model: "llama-3b"
  load_in_8bit: true

# 协同配置
collaboration:
  distillation_temperature: 4.0
  alignment_weight: 0.3
  joint_training: true
```

## 🎯 技术优势

### 相比联邦学习版本的优势
1. **架构简洁**: 去除多客户端复杂性
2. **训练高效**: 统一环境下端到端训练
3. **调试友好**: 更容易调试和优化
4. **部署简单**: 单一模型部署，无需分布式协调

### 核心创新点
1. **纯正协同**: 专注大小模型协同机制
2. **知识传递**: 高效的知识蒸馏策略
3. **特征融合**: 大小模型特征空间对齐
4. **联合优化**: 端到端联合训练

## 📈 预期效果

- **推荐精度**: 相比单一模型提升10-15%
- **推理速度**: 小模型快速推理，大模型精准优化
- **资源效率**: 合理分配计算资源
- **可解释性**: 更好的推荐可解释性

## 🔍 下一步计划

1. **模型优化**: 优化大小模型协同机制
2. **实验验证**: 多数据集验证效果
3. **性能调优**: 推理速度和精度平衡
4. **工程化**: 生产环境部署优化
