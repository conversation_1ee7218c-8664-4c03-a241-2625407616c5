"""
云端层 - LLM推荐服务器

实现云端的大语言模型推荐服务：
1. 接收客户端用户表示
2. LLM推荐优化
3. 推荐分数计算
4. Top-K推荐列表生成
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training

logger = logging.getLogger(__name__)


class LLMCloudServer(nn.Module):
    """
    云端LLM推荐服务器
    
    负责：
    1. 接收客户端用户表示μ
    2. LLM推荐优化处理
    3. 推荐分数计算
    4. Top-K推荐列表生成
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.device = config.get('device', 'cuda:0')
        
        # LLM配置
        self.llm_model_name = config.get('llm_model', 'llama-3b')
        self.load_in_8bit = config.get('load_in_8bit', True)
        self.item_num = config.get('item_num', 10000)
        self.user_repr_dim = config.get('user_repr_dim', 64)
        
        # 初始化LLM组件
        self._init_llm_components()
        
        # 初始化推荐组件
        self._init_recommendation_components()
        
        logger.info("LLMCloudServer initialized on cloud device")
    
    def _init_llm_components(self):
        """初始化LLM组件"""
        # 加载预训练LLM模型
        if self.llm_model_name == 'llama-3b':
            model_id = "meta-llama/Llama-3.2-3B-Instruct"
        else:
            model_id = self.llm_model_name
        
        try:
            # 加载模型
            if self.load_in_8bit:
                self.llm_model = AutoModelForCausalLM.from_pretrained(
                    model_id,
                    device_map=self.device,
                    torch_dtype=torch.float16,
                    load_in_8bit=True
                )
            else:
                self.llm_model = AutoModelForCausalLM.from_pretrained(
                    model_id,
                    device_map=self.device,
                    torch_dtype=torch.float16
                )
            
            self.llm_tokenizer = AutoTokenizer.from_pretrained(model_id, use_fast=False)
            
            # 添加特殊Token
            special_tokens = {
                'pad_token': '[PAD]',
                'additional_special_tokens': [
                    '[UserRep]',      # 用户表示标记
                    '[RecommendStart]', # 推荐开始标记
                    '[RecommendEnd]'    # 推荐结束标记
                ]
            }
            
            self.llm_tokenizer.add_special_tokens(special_tokens)
            self.llm_model.resize_token_embeddings(len(self.llm_tokenizer))
            
            # 准备模型进行微调
            self.llm_model = prepare_model_for_kbit_training(self.llm_model)
            
            # 冻结大部分参数
            for param in self.llm_model.parameters():
                param.requires_grad = False
            
            self.llm_hidden_size = self.llm_model.config.hidden_size
            
        except Exception as e:
            logger.warning(f"Failed to load LLM model: {e}, using mock model")
            # 使用模拟模型
            self.llm_hidden_size = 2048
            self.llm_model = None
            self.llm_tokenizer = None
    
    def _init_recommendation_components(self):
        """初始化推荐组件"""
        # 用户表示投影层
        self.user_repr_projector = nn.Sequential(
            nn.Linear(self.user_repr_dim, self.llm_hidden_size // 2),
            nn.LayerNorm(self.llm_hidden_size // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(self.llm_hidden_size // 2, self.llm_hidden_size)
        )
        
        # 推荐优化网络
        self.recommendation_optimizer = nn.Sequential(
            nn.Linear(self.llm_hidden_size, self.llm_hidden_size * 2),
            nn.LayerNorm(self.llm_hidden_size * 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(self.llm_hidden_size * 2, self.llm_hidden_size)
        )
        
        # 推荐分数计算网络
        self.score_calculator = nn.Sequential(
            nn.Linear(self.llm_hidden_size, self.llm_hidden_size // 2),
            nn.LayerNorm(self.llm_hidden_size // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(self.llm_hidden_size // 2, self.item_num)
        )
        
        # 物品嵌入（用于计算相似度）
        self.item_embeddings = nn.Embedding(self.item_num, self.llm_hidden_size)
        nn.init.normal_(self.item_embeddings.weight, mean=0, std=0.02)
    
    def generate_recommendation_prompt(self, user_id: str, user_context: str = "") -> str:
        """
        生成推荐提示
        
        Args:
            user_id: 用户ID
            user_context: 用户上下文信息
            
        Returns:
            prompt: 推荐提示文本
        """
        base_prompt = f"""
        Based on the user representation [UserRep], generate personalized recommendations.
        
        User ID: {user_id}
        Context: {user_context}
        
        [RecommendStart]
        Please recommend items that match the user's preferences based on their historical behavior patterns.
        [RecommendEnd]
        """
        
        return base_prompt.strip()
    
    def process_user_representation(self, user_mu: torch.Tensor, 
                                  user_id: str = "unknown") -> Dict[str, torch.Tensor]:
        """
        处理用户表示并生成推荐
        
        Args:
            user_mu: 用户表示μ [user_repr_dim]
            user_id: 用户ID
            
        Returns:
            recommendation_results: 推荐结果
        """
        batch_size = 1 if user_mu.dim() == 1 else user_mu.size(0)
        if user_mu.dim() == 1:
            user_mu = user_mu.unsqueeze(0)
        
        # 1. 用户表示投影到LLM空间
        projected_user_repr = self.user_repr_projector(user_mu)
        
        # 2. LLM推荐优化
        if self.llm_model is not None:
            # 使用真实LLM模型
            optimized_repr = self._llm_optimization(projected_user_repr, user_id)
        else:
            # 使用模拟优化
            optimized_repr = self.recommendation_optimizer(projected_user_repr)
        
        # 3. 推荐分数计算
        recommendation_scores = self.score_calculator(optimized_repr)
        
        # 4. 生成Top-K推荐
        top_k = self.config.get('top_k', 10)
        top_scores, top_items = torch.topk(recommendation_scores, k=top_k, dim=-1)
        
        return {
            'projected_user_repr': projected_user_repr,
            'optimized_repr': optimized_repr,
            'recommendation_scores': recommendation_scores,
            'top_items': top_items,
            'top_scores': top_scores
        }
    
    def _llm_optimization(self, user_repr: torch.Tensor, user_id: str) -> torch.Tensor:
        """
        使用LLM进行推荐优化
        
        Args:
            user_repr: 投影后的用户表示
            user_id: 用户ID
            
        Returns:
            optimized_repr: 优化后的表示
        """
        try:
            # 生成推荐提示
            prompt = self.generate_recommendation_prompt(user_id)
            
            # 分词
            tokens = self.llm_tokenizer(
                prompt,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=512
            ).to(self.device)
            
            # 获取输入嵌入
            inputs_embeds = self.llm_model.get_input_embeddings()(tokens['input_ids'])
            
            # 替换[UserRep]标记为实际用户表示
            user_rep_token_id = self.llm_tokenizer.convert_tokens_to_ids('[UserRep]')
            user_rep_positions = (tokens['input_ids'] == user_rep_token_id).nonzero(as_tuple=True)
            
            if len(user_rep_positions[0]) > 0:
                for batch_idx, pos in zip(user_rep_positions[0], user_rep_positions[1]):
                    inputs_embeds[batch_idx, pos] = user_repr[batch_idx]
            
            # LLM前向传播
            with torch.amp.autocast('cuda'):
                outputs = self.llm_model.forward(
                    inputs_embeds=inputs_embeds,
                    output_hidden_states=True
                )
            
            # 提取优化后的表示
            hidden_states = outputs.hidden_states[-1]
            optimized_repr = hidden_states.mean(dim=1)  # 平均池化
            
            return optimized_repr
            
        except Exception as e:
            logger.warning(f"LLM optimization failed: {e}, using fallback")
            return self.recommendation_optimizer(user_repr)
    
    def generate_top_k_recommendations(self, user_mu: torch.Tensor, 
                                     user_id: str = "unknown",
                                     top_k: int = 10) -> Dict[str, Any]:
        """
        生成Top-K推荐列表
        
        Args:
            user_mu: 用户表示
            user_id: 用户ID
            top_k: 推荐数量
            
        Returns:
            recommendations: 推荐结果
        """
        self.eval()
        with torch.no_grad():
            # 处理用户表示
            results = self.process_user_representation(user_mu, user_id)
            
            # 提取Top-K结果
            top_items = results['top_items'].squeeze(0).tolist()
            top_scores = results['top_scores'].squeeze(0).tolist()
            
            # 生成推荐解释
            explanations = self._generate_explanations(top_items, user_mu)
            
            return {
                'user_id': user_id,
                'recommendations': top_items,
                'scores': top_scores,
                'explanations': explanations,
                'recommendation_quality': self._assess_recommendation_quality(results)
            }
    
    def _generate_explanations(self, recommended_items: List[int], 
                             user_mu: torch.Tensor) -> List[str]:
        """
        生成推荐解释
        
        Args:
            recommended_items: 推荐物品列表
            user_mu: 用户表示
            
        Returns:
            explanations: 推荐解释列表
        """
        explanations = []
        
        for item_id in recommended_items:
            # 计算物品与用户的相似度
            item_emb = self.item_embeddings(torch.tensor([item_id], device=self.device))
            user_proj = self.user_repr_projector(user_mu.unsqueeze(0))
            
            similarity = F.cosine_similarity(item_emb, user_proj, dim=-1).item()
            
            explanation = f"物品 {item_id}: 与用户偏好相似度 {similarity:.3f}"
            explanations.append(explanation)
        
        return explanations
    
    def _assess_recommendation_quality(self, results: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        评估推荐质量
        
        Args:
            results: 推荐结果
            
        Returns:
            quality_metrics: 质量指标
        """
        top_scores = results['top_scores'].squeeze(0)
        
        return {
            'score_diversity': top_scores.std().item(),
            'score_mean': top_scores.mean().item(),
            'score_range': (top_scores.max() - top_scores.min()).item(),
            'confidence': torch.sigmoid(top_scores[0]).item()  # 最高分的置信度
        }
    
    def batch_recommend(self, user_representations: torch.Tensor, 
                       user_ids: List[str]) -> List[Dict[str, Any]]:
        """
        批量推荐
        
        Args:
            user_representations: 批量用户表示 [batch_size, user_repr_dim]
            user_ids: 用户ID列表
            
        Returns:
            batch_recommendations: 批量推荐结果
        """
        batch_recommendations = []
        
        for i, (user_mu, user_id) in enumerate(zip(user_representations, user_ids)):
            recommendation = self.generate_top_k_recommendations(user_mu, user_id)
            batch_recommendations.append(recommendation)
        
        return batch_recommendations


class CloudCommunicator:
    """
    云端通信器
    
    负责与客户端和知识蒸馏层的通信
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.knowledge_distillation_endpoint = config.get('kd_endpoint', 'http://localhost:8081')
    
    def receive_user_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        接收客户端请求
        
        Args:
            request_data: 请求数据
            
        Returns:
            processed_request: 处理后的请求
        """
        return {
            'user_id': request_data.get('user_id', 'unknown'),
            'user_representation': torch.tensor(request_data['user_representation']),
            'timestamp': request_data.get('timestamp', 0.0)
        }
    
    def send_to_knowledge_distillation(self, recommendation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送数据到知识蒸馏层
        
        Args:
            recommendation_data: 推荐数据
            
        Returns:
            kd_response: 知识蒸馏层响应
        """
        # 模拟发送到知识蒸馏层
        kd_request = {
            'recommendation_results': recommendation_data,
            'model_state': 'cloud_model_state',
            'performance_metrics': recommendation_data.get('recommendation_quality', {})
        }
        
        # 模拟知识蒸馏层响应
        mock_response = {
            'teacher_knowledge': {
                'distilled_features': torch.randn(64),
                'optimization_hints': "增强序列建模能力"
            },
            'distillation_loss': 0.05,
            'update_required': True
        }
        
        return mock_response


def demo_cloud_functionality():
    """演示云端功能"""
    print("☁️  云端层功能演示")
    print("=" * 50)
    
    # 配置
    config = {
        'item_num': 1000,
        'user_repr_dim': 64,
        'llm_model': 'llama-3b',
        'load_in_8bit': True,
        'top_k': 10,
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu'
    }
    
    # 初始化云端服务器
    cloud_server = LLMCloudServer(config)
    communicator = CloudCommunicator(config)
    
    print(f"✅ 云端LLM服务器初始化完成")
    print(f"📊 LLM隐藏层大小: {cloud_server.llm_hidden_size}")
    
    # 模拟接收客户端用户表示
    user_mu = torch.randn(64)  # 来自客户端的用户表示μ
    user_id = "user_001"
    
    print(f"\n📥 接收用户表示: {user_id}")
    print(f"📈 用户表示μ形状: {user_mu.shape}")
    print(f"📈 用户表示μ范数: {torch.norm(user_mu).item():.4f}")
    
    # 生成推荐
    recommendations = cloud_server.generate_top_k_recommendations(user_mu, user_id)
    
    print(f"\n🎯 推荐结果:")
    print(f"   - Top-5推荐: {recommendations['recommendations'][:5]}")
    print(f"   - 推荐分数: {[f'{score:.3f}' for score in recommendations['scores'][:5]]}")
    print(f"   - 推荐质量: 置信度={recommendations['recommendation_quality']['confidence']:.3f}")
    
    # 发送到知识蒸馏层
    kd_response = communicator.send_to_knowledge_distillation(recommendations)
    print(f"\n🧠 知识蒸馏反馈:")
    print(f"   - 蒸馏损失: {kd_response['distillation_loss']}")
    print(f"   - 优化建议: {kd_response['teacher_knowledge']['optimization_hints']}")
    
    print(f"\n💡 云端层特点:")
    print(f"   - 强大的LLM推荐优化能力")
    print(f"   - 高质量的推荐分数计算")
    print(f"   - 可解释的推荐结果")
    print(f"   - 与知识蒸馏层的协同工作")


if __name__ == '__main__':
    demo_cloud_functionality()
