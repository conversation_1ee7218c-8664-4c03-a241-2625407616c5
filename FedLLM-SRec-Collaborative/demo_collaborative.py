#!/usr/bin/env python3
"""
LLM-SRec大小模型协同演示脚本

展示边缘-云端大小模型协同推荐系统：
1. 边缘端CF-SRec小模型处理
2. 云端LLM大模型优化
3. 知识蒸馏协同学习
4. 端云协同推荐服务
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np
import logging
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.federated.collaborative_model import CollaborativeRecommendationModel
from training.collaborative_trainer import CollaborativeTrainer
from utils.fed_utils import setup_logging, set_random_seed, log_system_info


def create_demo_config() -> Dict[str, Any]:
    """创建协同演示配置"""
    config = {
        'experiment_name': 'collaborative_demo',
        'seed': 42,
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        
        # 数据配置
        'data': {
            'dataset': 'Movies_and_TV',
            'data_dir': './demo_data',
            'max_sequence_length': 50,
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1
        },
        
        # 边缘端小模型配置
        'client_model': {
            'item_num': 1000,
            'hidden_units': 32,
            'num_blocks': 1,
            'num_heads': 1,
            'dropout_rate': 0.1,
            'max_sequence_length': 50,
            'learning_rate': 1e-3,
            'batch_size': 16,
            'weight_decay': 1e-5,
            'gradient_clip': 1.0
        },
        
        # 云端大模型配置
        'server_model': {
            'llm_model': 'llama-3b',
            'distillation_temperature': 4.0,
            'learning_rate': 1e-4,
            'batch_size': 8,
            'weight_decay': 1e-6
        },
        
        # 协同机制配置
        'collaboration': {
            'distillation_temperature': 4.0,
            'loss_weights': {
                'recommendation_loss': 0.7,
                'distillation_loss': 0.3,
                'alignment_loss': 0.2,
                'regularization_loss': 0.1
            }
        },
        
        # 训练配置
        'training': {
            'max_epochs': 5,
            'patience': 3,
            'save_every': 2
        },
        
        # 协同训练配置
        'num_clients': 3,
        'training_rounds': 5,
        'local_epochs': 2,
        
        # 评估配置
        'evaluation': {
            'metrics': ['ndcg', 'hit_rate'],
            'top_k': [5, 10],
            'eval_batch_size': 32,
            'num_eval_samples': 100
        },
        
        # 日志配置
        'logging': {
            'log_dir': './demo_logs',
            'model_dir': './demo_models',
            'result_dir': './demo_results',
            'use_wandb': False,
            'use_tensorboard': False,
            'log_level': 'INFO'
        },
        
        # 硬件配置
        'hardware': {
            'num_workers': 0,
            'pin_memory': False,
            'persistent_workers': False
        },
        
        # 调试配置
        'debug': {
            'debug_mode': True,
            'sample_data': True,
            'sample_ratio': 0.1,
            'verbose': True
        }
    }
    
    return config


def demo_edge_cloud_architecture():
    """演示边缘-云端架构"""
    print("\n" + "="*60)
    print("🏗️ 边缘-云端架构演示")
    print("="*60)
    
    config = create_demo_config()
    
    # 创建边缘端小模型
    edge_model = CollaborativeRecommendationModel(config)
    
    # 创建云端大模型
    cloud_model = CollaborativeRecommendationModel(config)
    
    print(f"📊 模型架构对比:")
    
    # 统计参数数量
    edge_params = sum(p.numel() for p in edge_model.parameters())
    cloud_params = sum(p.numel() for p in cloud_model.parameters())
    
    print(f"   边缘端小模型参数: {edge_params:,}")
    print(f"   云端大模型参数: {cloud_params:,}")
    print(f"   参数比例: 1:{cloud_params//edge_params}")
    
    # 模拟数据流
    batch_size = 4
    seq_len = config['data']['max_sequence_length']
    user_sequences = torch.randint(1, 100, (batch_size, seq_len))
    
    print(f"\n🔄 数据流演示:")
    print(f"   输入序列形状: {user_sequences.shape}")
    
    # 边缘端处理
    with torch.no_grad():
        edge_output = edge_model.client_forward(user_sequences)
        print(f"   边缘端输出形状: {edge_output.shape}")
        
        # 云端处理
        cloud_output = cloud_model.server_forward(edge_output, None)
        print(f"   云端推荐分数形状: {cloud_output['recommendation_scores'].shape}")
    
    print("✅ 边缘-云端架构演示完成")


def demo_knowledge_distillation_flow():
    """演示知识蒸馏流程"""
    print("\n" + "="*60)
    print("🧠 知识蒸馏流程演示")
    print("="*60)
    
    config = create_demo_config()
    model = CollaborativeRecommendationModel(config)
    
    # 模拟数据
    batch_size = 8
    seq_len = config['data']['max_sequence_length']
    hidden_units = config['client_model']['hidden_units']
    
    user_sequences = torch.randint(1, 100, (batch_size, seq_len))
    user_representations = torch.randn(batch_size, hidden_units)
    
    print(f"📊 蒸馏流程:")
    
    # 1. 云端生成教师知识
    print(f"   1. 云端LLM生成教师知识...")
    with torch.no_grad():
        teacher_distributions = model.server_generate_teacher_knowledge(user_representations)
    
    print(f"      教师知识形状: {teacher_distributions.shape}")
    print(f"      概率分布和: {teacher_distributions.sum(dim=-1)[:3]}")  # 显示前3个
    
    # 2. 边缘端学习前的预测
    print(f"   2. 边缘端学习前预测...")
    with torch.no_grad():
        initial_repr = model.client_forward(user_sequences)
        initial_logits = model.objective_function['recommendation_head'](
            model.recommendation_optimizer(initial_repr)
        )
        initial_probs = F.softmax(initial_logits, dim=-1)
    
    initial_kl = F.kl_div(
        F.log_softmax(initial_logits / config['collaboration']['distillation_temperature'], dim=-1),
        teacher_distributions,
        reduction='batchmean'
    )
    print(f"      初始KL散度: {initial_kl.item():.4f}")
    
    # 3. 蒸馏学习过程
    print(f"   3. 执行蒸馏学习...")
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    
    for step in range(5):
        optimizer.zero_grad()
        kd_loss = model.client_distillation_learning(user_sequences, teacher_distributions)
        kd_loss.backward()
        optimizer.step()
        
        if step % 2 == 0:
            print(f"      步骤 {step}: 蒸馏损失 = {kd_loss.item():.4f}")
    
    # 4. 学习后的效果
    print(f"   4. 蒸馏学习后效果...")
    with torch.no_grad():
        final_repr = model.client_forward(user_sequences)
        final_logits = model.objective_function['recommendation_head'](
            model.recommendation_optimizer(final_repr)
        )
    
    final_kl = F.kl_div(
        F.log_softmax(final_logits / config['collaboration']['distillation_temperature'], dim=-1),
        teacher_distributions,
        reduction='batchmean'
    )
    
    improvement = ((initial_kl - final_kl) / initial_kl * 100).item()
    print(f"      最终KL散度: {final_kl.item():.4f}")
    print(f"      改善程度: {improvement:.1f}%")
    
    print("✅ 知识蒸馏流程演示完成")


def demo_collaborative_training():
    """演示协同训练过程"""
    print("\n" + "="*60)
    print("🚀 协同训练演示")
    print("="*60)
    
    config = create_demo_config()
    
    print(f"⚙️ 训练配置:")
    print(f"   边缘设备数量: {config['num_clients']}")
    print(f"   训练轮数: {config['training_rounds']}")
    print(f"   本地训练轮数: {config['local_epochs']}")
    print(f"   设备: {config['device']}")
    
    try:
        # 准备数据
        from utils.data_utils import prepare_sample_data
        prepare_sample_data(config['data']['data_dir'], config['data']['dataset'])
        
        # 创建训练器
        print(f"\n🔧 初始化协同训练器...")
        trainer = CollaborativeTrainer(config)
        
        print(f"\n🏃 开始协同训练演示...")
        
        # 运行一轮训练作为演示
        round_metrics = trainer.train_one_round()
        
        print(f"\n📈 训练结果:")
        if 'global_metrics' in round_metrics:
            global_metrics = round_metrics['global_metrics']
            for metric_name, value in global_metrics.items():
                if isinstance(value, (int, float)) and 'time' not in metric_name:
                    print(f"   {metric_name}: {value:.4f}")
        
        # 显示边缘端指标
        if 'edge_metrics' in round_metrics:
            edge_metrics = round_metrics['edge_metrics']
            avg_edge_loss = np.mean([m['train_loss'] for m in edge_metrics])
            avg_edge_ndcg = np.mean([m['val_ndcg'] for m in edge_metrics])
            print(f"   边缘端平均训练损失: {avg_edge_loss:.4f}")
            print(f"   边缘端平均验证NDCG: {avg_edge_ndcg:.4f}")
        
        # 显示蒸馏指标
        if 'distillation_metrics' in round_metrics:
            distill_metrics = round_metrics['distillation_metrics']
            valid_distill = [m for m in distill_metrics if m['num_batches'] > 0]
            if valid_distill:
                avg_distill_loss = np.mean([m['distillation_loss'] for m in valid_distill])
                print(f"   平均蒸馏损失: {avg_distill_loss:.4f}")
        
        print("✅ 协同训练演示完成")
        
    except Exception as e:
        print(f"❌ 训练演示出错: {str(e)}")
        print("💡 这可能是因为环境配置问题，但演示代码结构是正确的")


def demo_recommendation_service():
    """演示推荐服务"""
    print("\n" + "="*60)
    print("🎯 推荐服务演示")
    print("="*60)
    
    config = create_demo_config()
    
    # 创建模型
    edge_model = CollaborativeRecommendationModel(config)
    cloud_model = CollaborativeRecommendationModel(config)
    
    # 模拟用户请求
    batch_size = 3
    seq_len = config['data']['max_sequence_length']
    user_sequences = torch.randint(1, 100, (batch_size, seq_len))
    
    print(f"📱 模拟用户请求:")
    print(f"   用户数量: {batch_size}")
    print(f"   交互序列长度: {seq_len}")
    
    # 完整的推荐服务流程
    print(f"\n🔄 推荐服务流程:")
    
    # 1. 边缘端处理
    print(f"   1. 边缘端CF-SRec处理用户序列...")
    with torch.no_grad():
        user_representations = edge_model.client_forward(user_sequences)
    print(f"      生成用户表示: {user_representations.shape}")
    
    # 2. 云端处理
    print(f"   2. 云端LLM优化和推荐生成...")
    with torch.no_grad():
        cloud_outputs = cloud_model.server_forward(user_representations, None)
        recommendation_scores = cloud_outputs['recommendation_scores']
    print(f"      推荐分数: {recommendation_scores.shape}")
    
    # 3. 生成Top-K推荐
    top_k = 5
    print(f"   3. 生成Top-{top_k}推荐列表...")
    with torch.no_grad():
        top_scores, top_items = torch.topk(recommendation_scores, top_k, dim=-1)
    
    print(f"      推荐结果:")
    for i in range(batch_size):
        items = top_items[i].tolist()
        scores = top_scores[i].tolist()
        print(f"        用户{i}: 物品{items}, 分数{[f'{s:.3f}' for s in scores]}")
    
    # 4. 返回给用户
    print(f"   4. 推荐结果返回用户设备 ✅")
    
    print("✅ 推荐服务演示完成")


def main():
    """主演示函数"""
    print("🎉 欢迎使用LLM-SRec大小模型协同演示！")
    print("\n这个演示将展示边缘-云端大小模型协同推荐系统：")
    print("1. 边缘-云端架构对比")
    print("2. 知识蒸馏流程")
    print("3. 协同训练过程")
    print("4. 推荐服务流程")
    
    # 记录系统信息
    log_system_info()
    
    # 设置日志和随机种子
    config = create_demo_config()
    setup_logging(config['logging'])
    set_random_seed(config['seed'])
    
    # 运行各个演示
    demo_edge_cloud_architecture()
    demo_knowledge_distillation_flow()
    demo_collaborative_training()
    demo_recommendation_service()
    
    print("\n" + "="*60)
    print("🎊 大小模型协同演示完成！")
    print("="*60)
    print("\n📚 核心特色:")
    print("1. ⚡ 边缘端轻量级CF-SRec模型 - 快速响应，隐私保护")
    print("2. ☁️ 云端大型LLM模型 - 强大能力，全局优化")
    print("3. 🧠 知识蒸馏机制 - 大模型教导小模型")
    print("4. 🔄 端云协同架构 - 优势互补，性能提升")
    print("\n🚀 这就是纯正的大小模型协同推荐系统！")
    print("\n💡 接下来您可以:")
    print("   - 运行完整训练: bash scripts/run_collaborative_training.sh")
    print("   - 调整模型配置: 修改 config/collaborative_config.yaml")
    print("   - 扩展协同机制: 添加新的知识传递方法")


if __name__ == '__main__':
    main()
