# LLM-SRec大小模型协同推荐系统配置文件
# 专注于边缘-云端协同，不包含联邦学习组件

experiment_name: "llm_srec_collaborative"
seed: 42
device: "cuda:0"
log_level: "INFO"

# 数据配置
data:
  dataset: "Movies_and_TV"
  data_dir: "./data"
  raw_data_dir: "./data/raw"
  processed_data_dir: "./data/processed"
  federated_data_dir: "./data/collaborative"  # 重命名为collaborative
  min_interactions: 5
  max_sequence_length: 128
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1

# 边缘端小模型配置（复用client_model配置）
client_model:
  model_type: "cf_srec"
  item_num: 10000
  hidden_units: 64
  num_blocks: 2
  num_heads: 1
  dropout_rate: 0.2
  max_sequence_length: 128
  use_lora: true
  lora_r: 8
  lora_alpha: 16
  lora_dropout: 0.1
  learning_rate: 1e-4
  batch_size: 32
  weight_decay: 1e-5
  gradient_clip: 1.0

# 云端大模型配置（复用server_model配置）
server_model:
  model_type: "llm_srec"
  llm_model: "llama-3b"
  load_in_8bit: true
  max_length: 512
  temperature: 1.0
  top_p: 0.9
  distillation_temperature: 4.0
  distillation_weight: 0.5
  learning_rate: 1e-5
  batch_size: 16
  weight_decay: 1e-6
  gradient_accumulation_steps: 2

# 协同机制配置
collaboration:
  collaboration_method: "knowledge_distillation"
  distillation_temperature: 4.0
  loss_weights:
    recommendation_loss: 0.7
    distillation_loss: 0.3
    alignment_loss: 0.2
    regularization_loss: 0.1

# 训练配置
training:
  max_epochs: 50
  patience: 5
  save_every: 5
  eval_every: 1
  lr_scheduler: "cosine"
  warmup_steps: 100
  use_fp16: true
  gradient_checkpointing: false

# 协同训练特定配置
num_clients: 5  # 模拟的边缘设备数量
training_rounds: 20  # 协同训练轮数
local_epochs: 3  # 边缘端本地训练轮数

# 评估配置
evaluation:
  metrics: ["ndcg", "hit_rate", "recall", "precision"]
  top_k: [5, 10, 20]
  eval_batch_size: 64
  num_eval_samples: 1000
  privacy_metrics: []  # 移除隐私评估指标

# 日志和保存配置
logging:
  log_dir: "./experiments/logs"
  model_dir: "./experiments/models"
  result_dir: "./experiments/results"
  use_wandb: false
  wandb_project: "llm-srec-collaborative"
  wandb_entity: "your_entity"
  use_tensorboard: true
  tensorboard_dir: "./experiments/logs/tensorboard"

# 硬件配置
hardware:
  num_workers: 4
  pin_memory: true
  persistent_workers: true
  distributed: false
  world_size: 1
  rank: 0

# 调试配置
debug:
  debug_mode: false
  sample_data: false
  sample_ratio: 0.1
  verbose: true

# 边缘-云端协同特定配置
edge_cloud:
  # 边缘端配置
  edge:
    model_compression: true  # 边缘端模型压缩
    local_cache_size: 1000   # 本地缓存大小
    update_frequency: 1      # 更新频率（轮数）
    
  # 云端配置
  cloud:
    batch_processing: true   # 批量处理
    knowledge_cache: true    # 知识缓存
    adaptive_temperature: false  # 自适应温度
    
  # 通信配置
  communication:
    compression_method: "none"  # 通信压缩方法
    encryption: false       # 通信加密
    timeout: 30            # 通信超时（秒）

# 模型特定配置
model_specific:
  # CF-SRec小模型特定配置
  cf_srec:
    attention_dropout: 0.1
    hidden_dropout: 0.1
    layer_norm_eps: 1e-12
    initializer_range: 0.02
    
  # LLM大模型特定配置
  llm:
    model_name_or_path: "meta-llama/Llama-2-7b-hf"
    trust_remote_code: true
    torch_dtype: "float16"
    device_map: "auto"
    
# 实验配置
experiment:
  # 对比实验设置
  baselines:
    - "cf_srec_only"      # 仅CF-SRec
    - "llm_only"          # 仅LLM
    - "no_distillation"   # 无知识蒸馏
    
  # 消融实验设置
  ablation:
    - "no_alignment_loss"     # 无对齐损失
    - "no_regularization"     # 无正则化
    - "different_temperature" # 不同蒸馏温度
    
  # 超参数搜索
  hyperparameter_search:
    distillation_temperature: [2.0, 4.0, 6.0, 8.0]
    loss_weights:
      recommendation_loss: [0.5, 0.7, 0.9]
      distillation_loss: [0.1, 0.3, 0.5]
    learning_rates:
      edge: [1e-5, 1e-4, 1e-3]
      cloud: [1e-6, 1e-5, 1e-4]
