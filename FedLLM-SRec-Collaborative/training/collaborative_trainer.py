"""
LLM-SRec协同训练器 - 大小模型协同训练

实现边缘-云端大小模型协同推荐系统的训练流程：
1. 边缘端CF-SRec小模型训练
2. 云端LLM大模型优化
3. 知识蒸馏协同学习
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import logging
import yaml
from typing import Dict, List, Tuple, Optional, Any
from tqdm import tqdm
import wandb
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.federated.collaborative_model import CollaborativeRecommendationModel
from utils.data_utils import FederatedDataLoader
from utils.evaluation import RecommendationEvaluator
from utils.fed_utils import setup_logging, save_checkpoint, load_checkpoint

logger = logging.getLogger(__name__)


class CollaborativeTrainer:
    """
    大小模型协同训练器
    
    实现边缘-云端协同推荐系统的训练流程：
    1. 边缘端小模型本地训练
    2. 云端大模型知识生成
    3. 知识蒸馏协同学习
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = config.get('device', 'cuda:0')
        self.num_clients = config.get('num_clients', 5)  # 模拟多个边缘设备
        self.training_rounds = config.get('training_rounds', 20)
        self.local_epochs = config.get('local_epochs', 3)
        
        # 初始化组件
        self._init_logging()
        self._init_models()
        self._init_data()
        self._init_optimizers()
        self._init_evaluator()
        
        # 训练状态
        self.current_round = 0
        self.best_performance = 0.0
        self.patience_counter = 0
        
        logger.info("CollaborativeTrainer initialized successfully")
    
    def _init_logging(self):
        """初始化日志系统"""
        setup_logging(self.config['logging'])
        
        # 初始化Wandb
        if self.config['logging'].get('use_wandb', False):
            wandb.init(
                project=self.config['logging']['wandb_project'],
                entity=self.config['logging'].get('wandb_entity'),
                config=self.config,
                name=f"llm_srec_collaborative_{self.config['experiment_name']}"
            )
        
        # 初始化TensorBoard
        if self.config['logging'].get('use_tensorboard', True):
            self.tb_writer = SummaryWriter(
                log_dir=os.path.join(
                    self.config['logging']['tensorboard_dir'],
                    self.config['experiment_name']
                )
            )
    
    def _init_models(self):
        """初始化模型"""
        # 创建云端大模型
        self.cloud_model = CollaborativeRecommendationModel(self.config).to(self.device)
        
        # 创建边缘端小模型列表（模拟多个设备）
        self.edge_models = []
        for i in range(self.num_clients):
            edge_model = CollaborativeRecommendationModel(self.config).to(self.device)
            # 初始化为相同参数
            edge_model.load_state_dict(self.cloud_model.state_dict())
            self.edge_models.append(edge_model)
        
        logger.info(f"Initialized {self.num_clients} edge models and 1 cloud model")
    
    def _init_data(self):
        """初始化数据加载器"""
        self.data_loader = FederatedDataLoader(self.config)
        
        # 获取数据分割（复用联邦数据分割逻辑）
        self.client_train_loaders, self.client_val_loaders, self.test_loader = \
            self.data_loader.get_federated_dataloaders()
        
        logger.info(f"Initialized data loaders for {self.num_clients} edge devices")
    
    def _init_optimizers(self):
        """初始化优化器"""
        edge_config = self.config['client_model']  # 复用客户端配置
        cloud_config = self.config['server_model']  # 复用服务器配置
        
        # 边缘端优化器
        self.edge_optimizers = []
        for edge_model in self.edge_models:
            optimizer = torch.optim.AdamW(
                edge_model.parameters(),
                lr=edge_config['learning_rate'],
                weight_decay=edge_config.get('weight_decay', 1e-5)
            )
            self.edge_optimizers.append(optimizer)
        
        # 云端优化器
        self.cloud_optimizer = torch.optim.AdamW(
            self.cloud_model.parameters(),
            lr=cloud_config['learning_rate'],
            weight_decay=cloud_config.get('weight_decay', 1e-6)
        )
        
        logger.info("Initialized optimizers for edge and cloud models")
    
    def _init_evaluator(self):
        """初始化评估器"""
        self.evaluator = RecommendationEvaluator(self.config)
        logger.info("Initialized recommendation evaluator")
    
    def edge_local_training(self, edge_id: int, num_epochs: int) -> Dict[str, float]:
        """
        边缘端本地训练
        
        Args:
            edge_id: 边缘设备ID
            num_epochs: 本地训练轮数
            
        Returns:
            metrics: 训练指标
        """
        edge_model = self.edge_models[edge_id]
        optimizer = self.edge_optimizers[edge_id]
        train_loader = self.client_train_loaders[edge_id]
        
        edge_model.train()
        total_loss = 0.0
        num_batches = 0
        
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            
            for batch_idx, batch in enumerate(train_loader):
                # 移动数据到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                optimizer.zero_grad()
                
                # 边缘端前向传播
                outputs = edge_model(batch, mode='client_only')
                
                # 计算损失
                targets = {
                    'target_items': batch['target_items'],
                    'client_features': outputs['user_representations']
                }
                losses = edge_model.compute_loss(outputs, targets)
                
                # 反向传播
                losses['total_loss'].backward()
                
                # 梯度裁剪
                if self.config['client_model'].get('gradient_clip', 0) > 0:
                    torch.nn.utils.clip_grad_norm_(
                        edge_model.parameters(),
                        self.config['client_model']['gradient_clip']
                    )
                
                optimizer.step()
                
                epoch_loss += losses['total_loss'].item()
                num_batches += 1
            
            total_loss += epoch_loss
        
        avg_loss = total_loss / (num_epochs * len(train_loader))
        
        # 评估边缘端模型
        val_metrics = self.evaluate_edge_model(edge_id)
        
        metrics = {
            'train_loss': avg_loss,
            'val_ndcg': val_metrics.get('ndcg@10', 0.0),
            'val_hit_rate': val_metrics.get('hit_rate@10', 0.0)
        }
        
        logger.info(f"Edge {edge_id} training completed. Loss: {avg_loss:.4f}, "
                   f"Val NDCG@10: {metrics['val_ndcg']:.4f}")
        
        return metrics
    
    def cloud_knowledge_generation(self, edge_representations: List[torch.Tensor]) -> Dict[int, torch.Tensor]:
        """
        云端知识生成
        
        Args:
            edge_representations: 边缘端用户表示列表
            
        Returns:
            teacher_knowledge: 每个边缘设备对应的教师知识
        """
        self.cloud_model.eval()
        teacher_knowledge = {}
        
        logger.info("Generating teacher knowledge from cloud LLM")
        
        with torch.no_grad():
            for edge_id, user_representations in enumerate(edge_representations):
                if user_representations.shape[0] > 0:
                    # 云端LLM生成教师分布
                    teacher_distributions = self.cloud_model.server_generate_teacher_knowledge(
                        user_representations
                    )
                    teacher_knowledge[edge_id] = teacher_distributions
                    
                    logger.debug(f"Generated teacher knowledge for edge {edge_id}: "
                               f"shape {teacher_distributions.shape}")
                else:
                    # 如果边缘设备没有数据，创建空的教师知识
                    teacher_knowledge[edge_id] = torch.empty(0, self.cloud_model.item_num).to(self.device)
        
        logger.info(f"Teacher knowledge generation completed for {len(teacher_knowledge)} edge devices")
        
        return teacher_knowledge
    
    def edge_distillation_learning(self, edge_id: int, teacher_distributions: torch.Tensor) -> Dict[str, float]:
        """
        边缘端蒸馏学习
        
        Args:
            edge_id: 边缘设备ID
            teacher_distributions: 教师分布
            
        Returns:
            metrics: 蒸馏学习指标
        """
        edge_model = self.edge_models[edge_id]
        optimizer = self.edge_optimizers[edge_id]
        train_loader = self.client_train_loaders[edge_id]
        
        if len(train_loader) == 0:
            logger.warning(f"Edge {edge_id} has no training data, skipping distillation")
            return {'distillation_loss': 0.0, 'num_batches': 0}
        
        edge_model.train()
        total_loss = 0.0
        total_rec_loss = 0.0
        total_kd_loss = 0.0
        num_batches = 0
        
        # 蒸馏权重
        alpha = self.config['collaboration']['loss_weights'].get('recommendation_loss', 0.7)
        beta = self.config['collaboration']['loss_weights'].get('distillation_loss', 0.3)
        
        for batch_idx, batch in enumerate(train_loader):
            # 移动数据到设备
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            optimizer.zero_grad()
            
            # 1. 计算推荐损失
            outputs = edge_model(batch, mode='client_only')
            targets = {'target_items': batch['target_items']}
            rec_losses = edge_model.compute_loss(outputs, targets)
            rec_loss = rec_losses.get('recommendation_loss', rec_losses['total_loss'])
            
            # 2. 计算知识蒸馏损失
            batch_size = batch['user_sequences'].shape[0]
            if teacher_distributions.shape[0] >= batch_size:
                batch_teacher_dist = teacher_distributions[:batch_size]
            else:
                # 如果教师分布不够，重复使用
                repeat_times = (batch_size + teacher_distributions.shape[0] - 1) // teacher_distributions.shape[0]
                expanded_teacher = teacher_distributions.repeat(repeat_times, 1)
                batch_teacher_dist = expanded_teacher[:batch_size]
            
            kd_loss = edge_model.compute_distillation_loss_only(
                batch['user_sequences'], batch_teacher_dist
            )
            
            # 3. 联合损失
            total_batch_loss = alpha * rec_loss + beta * kd_loss
            
            # 反向传播
            total_batch_loss.backward()
            
            # 梯度裁剪
            if self.config['client_model'].get('gradient_clip', 0) > 0:
                torch.nn.utils.clip_grad_norm_(
                    edge_model.parameters(),
                    self.config['client_model']['gradient_clip']
                )
            
            optimizer.step()
            
            # 记录损失
            total_loss += total_batch_loss.item()
            total_rec_loss += rec_loss.item()
            total_kd_loss += kd_loss.item()
            num_batches += 1
        
        # 计算平均损失
        avg_total_loss = total_loss / num_batches if num_batches > 0 else 0.0
        avg_rec_loss = total_rec_loss / num_batches if num_batches > 0 else 0.0
        avg_kd_loss = total_kd_loss / num_batches if num_batches > 0 else 0.0
        
        metrics = {
            'total_loss': avg_total_loss,
            'recommendation_loss': avg_rec_loss,
            'distillation_loss': avg_kd_loss,
            'num_batches': num_batches
        }
        
        logger.info(f"Edge {edge_id} distillation learning completed. "
                   f"Total Loss: {avg_total_loss:.4f}, Rec Loss: {avg_rec_loss:.4f}, "
                   f"KD Loss: {avg_kd_loss:.4f}")
        
        return metrics
    
    def evaluate_edge_model(self, edge_id: int) -> Dict[str, float]:
        """评估边缘端模型"""
        edge_model = self.edge_models[edge_id]
        val_loader = self.client_val_loaders[edge_id]
        
        return self.evaluator.evaluate_model(edge_model, val_loader)
    
    def evaluate_cloud_model(self) -> Dict[str, float]:
        """评估云端模型"""
        return self.evaluator.evaluate_model(self.cloud_model, self.test_loader)
    
    def train_one_round(self) -> Dict[str, Any]:
        """
        训练一轮协同学习
        
        Returns:
            round_metrics: 本轮训练指标
        """
        logger.info(f"Starting collaborative round {self.current_round + 1}/{self.training_rounds}")
        
        round_metrics = {
            'round': self.current_round + 1,
            'edge_metrics': [],
            'cloud_metrics': {},
            'distillation_metrics': [],
            'global_metrics': {}
        }
        
        # 1. 边缘端本地训练
        edge_representations = []
        for edge_id in range(self.num_clients):
            logger.info(f"Local training for edge {edge_id + 1}/{self.num_clients}")
            
            edge_metrics = self.edge_local_training(edge_id, self.local_epochs)
            round_metrics['edge_metrics'].append(edge_metrics)
            
            # 收集边缘端用户表示
            edge_model = self.edge_models[edge_id]
            val_loader = self.client_val_loaders[edge_id]
            
            edge_model.eval()
            with torch.no_grad():
                batch_representations = []
                for batch in val_loader:
                    batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                            for k, v in batch.items()}
                    outputs = edge_model(batch, mode='client_only')
                    batch_representations.append(outputs['user_representations'])
                
                if batch_representations:
                    edge_repr = torch.cat(batch_representations, dim=0)
                    edge_representations.append(edge_repr)
                else:
                    edge_representations.append(torch.empty(0, self.config['client_model']['hidden_units']).to(self.device))
        
        # 2. 云端知识生成
        logger.info("Generating teacher knowledge from cloud LLM")
        teacher_knowledge = self.cloud_knowledge_generation(edge_representations)
        
        cloud_metrics = {
            'num_edges_with_knowledge': len([k for k, v in teacher_knowledge.items() if v.shape[0] > 0]),
            'total_teacher_samples': sum(v.shape[0] for v in teacher_knowledge.values())
        }
        round_metrics['cloud_metrics'] = cloud_metrics
        
        # 3. 边缘端蒸馏学习
        logger.info("Performing edge distillation learning")
        distillation_metrics = []
        for edge_id in range(self.num_clients):
            if edge_id in teacher_knowledge and teacher_knowledge[edge_id].shape[0] > 0:
                logger.info(f"Distillation learning for edge {edge_id + 1}/{self.num_clients}")
                
                distill_metrics = self.edge_distillation_learning(
                    edge_id, 
                    teacher_knowledge[edge_id]
                )
                distillation_metrics.append(distill_metrics)
            else:
                logger.warning(f"Edge {edge_id} has no teacher knowledge, skipping distillation")
                distillation_metrics.append({'distillation_loss': 0.0, 'num_batches': 0})
        
        round_metrics['distillation_metrics'] = distillation_metrics
        
        # 4. 全局评估
        logger.info("Evaluating cloud model")
        global_metrics = self.evaluate_cloud_model()
        round_metrics['global_metrics'] = global_metrics
        
        # 5. 记录指标
        self._log_metrics(round_metrics)
        
        # 6. 保存检查点
        if (self.current_round + 1) % self.config['training'].get('save_every', 5) == 0:
            self._save_checkpoint(round_metrics)
        
        self.current_round += 1
        
        # 计算平均蒸馏损失
        avg_distill_loss = np.mean([m['distillation_loss'] for m in distillation_metrics if m['num_batches'] > 0])
        
        logger.info(f"Round {self.current_round} completed. "
                   f"Cloud NDCG@10: {global_metrics.get('ndcg@10', 0.0):.4f}, "
                   f"Avg Distillation Loss: {avg_distill_loss:.4f}")
        
        return round_metrics
    
    def train(self):
        """主训练循环"""
        logger.info("Starting collaborative training")
        
        training_history = []
        
        for round_idx in range(self.training_rounds):
            try:
                round_metrics = self.train_one_round()
                training_history.append(round_metrics)
                
                # 早停检查
                current_performance = round_metrics['global_metrics'].get('ndcg@10', 0.0)
                if current_performance > self.best_performance:
                    self.best_performance = current_performance
                    self.patience_counter = 0
                    
                    # 保存最佳模型
                    self._save_best_model(round_metrics)
                else:
                    self.patience_counter += 1
                
                # 早停
                patience = self.config['training'].get('patience', 5)
                if self.patience_counter >= patience:
                    logger.info(f"Early stopping triggered after {patience} rounds without improvement")
                    break
                    
            except Exception as e:
                logger.error(f"Error in round {round_idx + 1}: {str(e)}")
                raise e
        
        logger.info("Collaborative training completed")
        
        # 最终评估
        final_metrics = self.evaluate_cloud_model()
        logger.info(f"Final performance - NDCG@10: {final_metrics.get('ndcg@10', 0.0):.4f}, "
                   f"Hit Rate@10: {final_metrics.get('hit_rate@10', 0.0):.4f}")
        
        return training_history
    
    def _log_metrics(self, metrics: Dict[str, Any]):
        """记录训练指标"""
        round_num = metrics['round']
        
        # 计算平均边缘端指标
        if metrics['edge_metrics']:
            avg_edge_loss = np.mean([m['train_loss'] for m in metrics['edge_metrics']])
            avg_edge_ndcg = np.mean([m['val_ndcg'] for m in metrics['edge_metrics']])
            
            # 计算平均蒸馏指标
            distill_metrics = metrics.get('distillation_metrics', [])
            if distill_metrics:
                valid_distill_metrics = [m for m in distill_metrics if m['num_batches'] > 0]
                if valid_distill_metrics:
                    avg_distill_loss = np.mean([m['distillation_loss'] for m in valid_distill_metrics])
                else:
                    avg_distill_loss = 0.0
            else:
                avg_distill_loss = 0.0
            
            # TensorBoard记录
            if hasattr(self, 'tb_writer'):
                self.tb_writer.add_scalar('Edge/AvgTrainLoss', avg_edge_loss, round_num)
                self.tb_writer.add_scalar('Edge/AvgValNDCG', avg_edge_ndcg, round_num)
                self.tb_writer.add_scalar('Distillation/AvgDistillationLoss', avg_distill_loss, round_num)
            
            # Wandb记录
            if self.config['logging'].get('use_wandb', False):
                log_data = {
                    'round': round_num,
                    'edge_avg_train_loss': avg_edge_loss,
                    'edge_avg_val_ndcg': avg_edge_ndcg,
                    'avg_distillation_loss': avg_distill_loss,
                    **metrics['global_metrics']
                }
                wandb.log(log_data)
            
            # 控制台日志
            logger.info(f"Round {round_num} Metrics Summary:")
            logger.info(f"  Edge Avg Train Loss: {avg_edge_loss:.4f}")
            logger.info(f"  Edge Avg Val NDCG: {avg_edge_ndcg:.4f}")
            logger.info(f"  Avg Distillation Loss: {avg_distill_loss:.4f}")
            
            if 'global_metrics' in metrics:
                cloud_ndcg = metrics['global_metrics'].get('ndcg@10', 0.0)
                cloud_hr = metrics['global_metrics'].get('hit_rate@10', 0.0)
                logger.info(f"  Cloud NDCG@10: {cloud_ndcg:.4f}")
                logger.info(f"  Cloud Hit Rate@10: {cloud_hr:.4f}")
    
    def _save_checkpoint(self, metrics: Dict[str, Any]):
        """保存检查点"""
        checkpoint_dir = os.path.join(self.config['logging']['model_dir'], 'checkpoints')
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        checkpoint_path = os.path.join(checkpoint_dir, f'round_{self.current_round}.pt')
        
        checkpoint = {
            'round': self.current_round,
            'cloud_model_state': self.cloud_model.state_dict(),
            'edge_model_states': [model.state_dict() for model in self.edge_models],
            'cloud_optimizer_state': self.cloud_optimizer.state_dict(),
            'edge_optimizer_states': [opt.state_dict() for opt in self.edge_optimizers],
            'metrics': metrics,
            'config': self.config
        }
        
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Checkpoint saved to {checkpoint_path}")
    
    def _save_best_model(self, metrics: Dict[str, Any]):
        """保存最佳模型"""
        best_model_dir = os.path.join(self.config['logging']['model_dir'], 'best')
        os.makedirs(best_model_dir, exist_ok=True)
        
        # 保存云端模型
        cloud_model_path = os.path.join(best_model_dir, 'cloud_model.pt')
        torch.save(self.cloud_model.state_dict(), cloud_model_path)
        
        # 保存边缘端模型
        for i, edge_model in enumerate(self.edge_models):
            edge_model_path = os.path.join(best_model_dir, f'edge_{i}_model.pt')
            torch.save(edge_model.state_dict(), edge_model_path)
        
        # 保存指标
        metrics_path = os.path.join(best_model_dir, 'metrics.yaml')
        with open(metrics_path, 'w') as f:
            yaml.dump(metrics, f)
        
        logger.info(f"Best model saved to {best_model_dir}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='LLM-SRec Collaborative Training')
    parser.add_argument('--config', type=str, default='config/collaborative_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--device', type=str, default='cuda:0',
                       help='Device to use for training')
    
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
    
    # 覆盖设备配置
    config['device'] = args.device
    
    # 创建训练器并开始训练
    trainer = CollaborativeTrainer(config)
    training_history = trainer.train()
    
    print("Collaborative training completed successfully!")


if __name__ == '__main__':
    main()
